package usecase

import (
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	userModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

type TyreAlertUseCase struct {
	DB                  database.DBUsecase
	tyreAlertRepository repository.TyreAlertRepository
	userRepo            userIdentityRepository.UserRepository
}

func NewTyreAlertUseCase(
	DB database.DBUsecase,
	tyreAlertRepository repository.TyreAlertRepository,
	userRepo userIdentityRepository.UserRepository,
) *TyreAlertUseCase {
	return &TyreAlertUseCase{
		DB:                  DB,
		tyreAlertRepository: tyreAlertRepository,
		userRepo:            userRepo,
	}
}

func (uc *TyreAlertUseCase) GetTyreAlertConfigByParentAssetID(ctx context.Context, parentAssetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	config, err := uc.tyreAlertRepository.GetTyreAlertConfig(ctx, uc.DB.DB(), models.TyreAlertConfigCondition{
		Where: models.TyreAlertConfigWhere{
			ParentAssetID: parentAssetID,
			ClientID:      claim.GetLoggedInClientID(),
			ShowDeleted:   false,
		},
		Preload: models.TyreAlertConfigPreload{},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if config == nil {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: parentAssetID,
			Data:        nil,
		}, nil
	}

	userMapNames := map[string]string{}
	userIDs := []string{}
	if config.TicketAssignedUserID.String != "" {
		userIDs = append(userIDs, config.TicketAssignedUserID.String)
	}
	for _, userID := range config.NotificationRecipientUserIDs {
		userIDs = append(userIDs, userID)
	}

	if len(userIDs) > 0 {
		users, err := uc.userRepo.GetUsersV2(ctx, uc.DB.DB(), userModel.UserCondition{
			Where: userModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, user := range users {
			userMapNames[user.ID] = user.GetName()
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: parentAssetID,
		Data:        dtos.BuildTyreAlertConfigResp(*config, userMapNames),
	}, nil
}

func (uc *TyreAlertUseCase) UpsertTyreAlertConfigByParentAssetID(ctx context.Context, parentAssetID string, req dtos.TyreAlertConfigReq) (*commonmodel.UpdateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	config := &models.TyreAlertConfig{
		ParentAssetID:                parentAssetID,
		UsePressureAlert:             req.UsePressureAlert,
		UseHighTemperatureAlert:      req.UseHighTemperatureAlert,
		MaxTemperatureThreshold:      req.MaxTemperatureThreshold,
		UseSendNotification:          req.UseSendNotification,
		NotificationActionTypes:      req.NotificationActionTypes,
		NotificationRecipientUserIDs: req.NotificationRecipientUserIDs,
		NotifyAssetAssignee:          req.NotifyAssetAssignee,
		UseCreateTickets:             req.UseCreateTickets,
		TicketAssignedUserID:         req.TicketAssignedUserID,
	}

	err = uc.tyreAlertRepository.UpsertTyreAlertConfig(ctx, tx.DB(), config)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: parentAssetID,
		Data:        nil,
	}, nil
}

func (uc *TyreAlertUseCase) GetTyreAlertsByParentAssetID(ctx context.Context, parentAssetID string, req dtos.TyreAlertListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	req.Normalize()

	totalRecords, alerts, err := uc.tyreAlertRepository.GetTyreAlertList(ctx, uc.DB.DB(), models.GetTyreAlertListParam{
		ListRequest: req.ListRequest,
		Cond: models.TyreAlertCondition{
			Where: models.TyreAlertWhere{
				ParentAssetID: parentAssetID,
				ClientID:      claim.GetLoggedInClientID(),
			},
			Preload:     models.TyreAlertPreload{},
			Columns:     []string{},
			IsForUpdate: false,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         dtos.BuildTyreAlertListResp(alerts),
	}, nil
}
