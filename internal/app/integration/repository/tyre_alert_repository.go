package repository

import (
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type TyreAlertRepository interface {
	// TyreAlertConfig operations
	GetTyreAlertConfig(ctx context.Context, dB database.DBI, cond models.TyreAlertConfigCondition) (*models.TyreAlertConfig, error)
	UpsertTyreAlertConfig(ctx context.Context, dB database.DBI, config *models.TyreAlertConfig) error

	// TyreAlert operations
	GetTyreAlertList(ctx context.Context, dB database.DBI, param models.GetTyreAlertListParam) (int, []models.TyreAlert, error)
	CreateTyreAlert(ctx context.Context, dB database.DBI, alert *models.TyreAlert) error
}
