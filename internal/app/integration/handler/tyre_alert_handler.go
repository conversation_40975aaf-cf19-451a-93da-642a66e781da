package handler

import (
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type TyreAlertHandler struct {
	tyreAlertUseCase *usecase.TyreAlertUseCase
}

func NewTyreAlertHandler(
	tyreAlertUseCase *usecase.TyreAlertUseCase,
) *TyreAlertHandler {
	return &TyreAlertHandler{
		tyreAlertUseCase: tyreAlertUseCase,
	}
}

func (h *TyreAlertHandler) GetTyreAlertConfigByParentAssetID(c *gin.Context) {
	ctx := c.Request.Context()
	parentAssetID := c.Param("parent_asset_id")

	if parentAssetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "parent_asset_id is required"})
		return
	}

	resp, err := h.tyreAlertUseCase.GetTyreAlertConfigByParentAssetID(ctx, parentAssetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TyreAlertHandler) UpsertTyreAlertConfigByParentAssetID(c *gin.Context) {
	ctx := c.Request.Context()
	parentAssetID := c.Param("parent_asset_id")

	if parentAssetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "parent_asset_id is required"})
		return
	}

	var req dtos.TyreAlertConfigReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.tyreAlertUseCase.UpsertTyreAlertConfigByParentAssetID(ctx, parentAssetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *TyreAlertHandler) GetTyreAlertsByParentAssetID(c *gin.Context) {
	ctx := c.Request.Context()
	parentAssetID := c.Param("parent_asset_id")

	if parentAssetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "parent_asset_id is required"})
		return
	}

	var req dtos.TyreAlertListReq
	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.tyreAlertUseCase.GetTyreAlertsByParentAssetID(ctx, parentAssetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
